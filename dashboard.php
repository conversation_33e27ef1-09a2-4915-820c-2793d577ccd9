<?php
include "includes/header.php";
include "includes/config.php";
if (!isset($_SESSION['user_id']) && $_SESSION['user_id'] == '') {
    header('Location: index.php');
}
?>
<body class="bg-gray-50 min-h-screen">
    <!-- Mobile Menu Button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <button id="mobile-menu-btn" onclick="toggleMobileMenu()" class="bg-white p-2 rounded-lg shadow-lg">
            <svg class="w-6 h-6 text-pakistan-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu">
            <div class="p-4 border-b">
                <h2 class="text-xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h2>
            </div>
            <nav class="p-4">
                <ul class="space-y-4">
                    
                    <li><button onclick="logout()" class="block w-full text-left text-red-600 hover:text-red-800">Logout</button></li>

                    <li><button  class="block w-full mt-8 text-left text-sm"><?php echo $_SESSION['fullname']; ?></button></li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg relative z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center ml-12 lg:ml-0">
                        <h1 class="text-xl sm:text-2xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h1>
                    </div>
                </div>
                <div class="hidden lg:flex items-center space-x-4">
                    <span class="text-gray-700 text-sm sm:text-base">Welcome, <?php echo $_SESSION['fullname']; ?></span>
                    <button onclick="logout()" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Logout</button>
                </div>
                <!-- Mobile user info -->
                <div class="lg:hidden hidden flex items-center">
                    <span class="text-gray-700 text-sm"><?php echo $_SESSION['fullname']; ?></span>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
        <!-- Dashboard Header -->
        <div class="mb-6 sm:mb-8">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Your Travel Dashboard</h1>
                    <p class="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">Manage your tours and plan new adventures</p>
                </div>
                <button onclick="showNewTourModal()" class="w-full sm:w-auto bg-pakistan-green hover:bg-pakistan-light text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold transition duration-300 text-sm sm:text-base">
                    + New Tour
                </button>
            </div>
        </div>

        

        <!-- Previous Tours -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-base sm:text-lg leading-6 font-medium text-gray-900">Your Previous Tours</h3>
                <p class="mt-1 max-w-2xl text-xs sm:text-sm text-gray-500">A list of all your completed and upcoming tours</p>
            </div>
            <ul class="divide-y divide-gray-200">
                <!-- Tour 1 -->
                <?php
                $uid = $_SESSION['user_id'];
                $query = "SELECT ut.tid, ut.date, t.* FROM usertours ut 
                         JOIN tour t ON ut.tid = t.id 
                         WHERE ut.uid = $uid";
                $result = mysqli_query($db, $query);
                
                while($row = mysqli_fetch_assoc($result)) {
                    $formatted_date = date("F j, Y", strtotime($row['date']));
                    echo '<li>
                        <div class="px-4 py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12 sm:h-16 sm:w-16">
                                    <div class="h-12 w-12 sm:h-16 sm:w-16 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                        <span class="text-white text-lg sm:text-2xl">' . $row['icon'] . '</span>
                                    </div>
                                </div>
                                <div class="ml-3 sm:ml-4 flex-1">
                                    <div class="text-sm sm:text-base font-medium text-gray-900">' . $row['title'] . '</div>
                                    <div class="text-xs sm:text-sm text-gray-500">' . $row['highlight_destinations'] . '</div>
                                    <div class="text-xs text-gray-400 mt-1">' . $formatted_date . ' • ' . $row['duration'] . ' days</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between sm:justify-end space-x-3 sm:space-x-4">
                                <button onclick="viewTourPlan(' . $row['tid'] . ')" class="text-pakistan-green hover:text-pakistan-light font-medium text-xs sm:text-sm">View Plan</button>
                            </div>
                        </div>
                    </li>';
                }
                ?>

                
            </ul>
        </div>

        <!-- Quick Actions (Mobile) -->
        <div class="lg:hidden mt-6 grid grid-cols-2 gap-4">
            <button onclick="showNewTourModal()" class="bg-pakistan-green text-white p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">➕</div>
                <div class="text-sm font-medium">New Tour</div>
            </button>
            <button onclick="window.location.href='#'" class="bg-blue-600 text-white p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">👤</div>
                <div class="text-sm font-medium">Profile</div>
            </button>
        </div>
    </div>

    <!-- New Tour Modal -->
    <div id="newTourModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 p-4">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto">
                <div class="sticky top-0 bg-white px-4 sm:px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Plan New Tour</h2>
                    <button onclick="closeNewTourModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                
                <div class="p-4 sm:p-6">
                    <form onsubmit="handleNewTour(event)">
                        <div class="grid grid-cols-1 gap-4 sm:gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tour Name</label>
                                <input type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base" placeholder="e.g., Northern Areas Adventure">
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                    <input type="date" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                    <input type="date" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of People</label>
                                    <select required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base">
                                        <option value="">Select...</option>
                                        <option value="1">1 Person</option>
                                        <option value="2">2 People</option>
                                        <option value="3-5">3-5 People</option>
                                        <option value="6-10">6-10 People</option>
                                        <option value="10+">10+ People</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                                    <select required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base">
                                        <option value="">Select...</option>
                                        <option value="budget">Budget (₨ 10,000 - 25,000)</option>
                                        <option value="mid">Mid-range (₨ 25,000 - 50,000)</option>
                                        <option value="luxury">Luxury (₨ 50,000+)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Destinations</label>
                                <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Lahore</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Karachi</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Islamabad</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Hunza Valley</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Skardu</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="checkbox" class="rounded border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Swat</span>
                                    </label>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tour Type</label>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                    <label class="flex items-center text-sm">
                                        <input type="radio" name="tourType" value="adventure" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Adventure & Trekking</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" name="tourType" value="cultural" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Cultural & Historical</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" name="tourType" value="family" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Family Friendly</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" name="tourType" value="luxury" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green mr-2">
                                        <span>Luxury Experience</span>
                                    </label>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Special Requirements</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green text-sm sm:text-base" placeholder="Any special requirements, dietary restrictions, accessibility needs, etc."></textarea>
                            </div>
                        </div>

                        <div class="mt-6 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                            <button type="button" onclick="closeNewTourModal()" class="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm sm:text-base">
                                Cancel
                            </button>
                            <button type="submit" class="w-full sm:w-auto px-6 py-2 bg-pakistan-green text-white rounded-md hover:bg-pakistan-light transition duration-300 text-sm sm:text-base">
                                Create Tour Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <script src="includes/media/central.js"></script>
    <script src="includes/media/dashboard.js"></script>
<?php
include "includes/footer.php";
?>