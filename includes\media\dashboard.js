     function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menu = document.getElementById('mobile-menu');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    menu.classList.remove('-translate-x-full');
                }, 10);
            } else {
                menu.classList.add('-translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }

        function showNewTourModal() {
            document.getElementById('newTourModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeNewTourModal() {
            document.getElementById('newTourModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function handleNewTour(event) {
            event.preventDefault();
            alert('Tour plan created successfully! Redirecting to tour details...');
            window.location.href = 'tour-plan.html';
        }

        function viewTourPlan(tourId) {
            window.location.href = `tour-plan.php?tour=${tourId}`;
        }

        
        // Close mobile menu when clicking overlay
        document.getElementById('mobile-menu-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('newTourModal');
            if (event.target === modal) {
                closeNewTourModal();
            }
        }

        // Check authentication
       