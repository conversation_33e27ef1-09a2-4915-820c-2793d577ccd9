 function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menu = document.getElementById('mobile-menu');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    menu.classList.remove('-translate-x-full');
                }, 10);
            } else {
                menu.classList.add('-translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('visitPakistanUser');
                window.location.href = 'index.html';
            }
        }

        function downloadItinerary() {
            alert('Downloading detailed itinerary PDF...');
        }

        function shareTrip() {
            if (navigator.share) {
                navigator.share({
                    title: 'Northern Areas Adventure - Visit Pakistan',
                    text: 'Check out my amazing trip to Northern Pakistan!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Trip link copied to clipboard!');
                });
            }
        }

        function bookAgain() {
            window.location.href = 'dashboard.html';
        }

        // Close mobile menu when clicking overlay
        document.getElementById('mobile-menu-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });

        