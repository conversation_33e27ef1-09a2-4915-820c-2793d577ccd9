<?php
include "includes/header.php";
include "includes/config.php";
?>

<body class="bg-gradient-to-br from-green-50 to-emerald-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <h1 class="text-2xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h1>
                    </div>
                </div>
                <?php
                if (isset($_SESSION['user_id'])) {
                    echo '<div class="flex items-center space-x-4">
      
                        <button onclick="window.location.href=\'dashboard.php\'" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Trips</button>
                        <button onclick="logout()" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Logout</button>
                    </div>';
                }else{
                ?>
                <div class="flex items-center space-x-4">
                    <button onclick="showLogin()" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Login</button>
                    <button onclick="showSignup()" class="bg-pakistan-green hover:bg-pakistan-light text-white px-4 py-2 rounded-md text-sm font-medium">Sign Up</button>
                </div>
                <?php
                }
                ?>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-pakistan-green to-pakistan-light">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
                Discover Pakistan
            </h1>
            <p class="mt-6 text-xl text-gray-300 max-w-3xl">
                From the mighty peaks of K2 to the ancient ruins of Mohenjo-daro, experience the incredible diversity and beauty of Pakistan with our expertly crafted tours.
            </p>
            <div class="mt-10">
                <button onclick="showSignup()" class="bg-white text-pakistan-green px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition duration-300">
                    Start Your Journey
                </button>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-extrabold text-gray-900">Why Choose Visit Pakistan?</h2>
                <p class="mt-4 text-lg text-gray-600">Experience the best of Pakistan with our comprehensive travel services</p>
            </div>
            <div class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                <div class="text-center">
                    <div class="bg-pakistan-green rounded-full w-16 h-16 flex items-center justify-center mx-auto">
                        <span class="text-white text-2xl">🏔️</span>
                    </div>
                    <h3 class="mt-4 text-xl font-semibold text-gray-900">Mountain Adventures</h3>
                    <p class="mt-2 text-gray-600">Explore the world's highest peaks including K2, Nanga Parbat, and more</p>
                </div>
                <div class="text-center">
                    <div class="bg-pakistan-green rounded-full w-16 h-16 flex items-center justify-center mx-auto">
                        <span class="text-white text-2xl">🏛️</span>
                    </div>
                    <h3 class="mt-4 text-xl font-semibold text-gray-900">Rich Heritage</h3>
                    <p class="mt-2 text-gray-600">Discover ancient civilizations and UNESCO World Heritage sites</p>
                </div>
                <div class="text-center">
                    <div class="bg-pakistan-green rounded-full w-16 h-16 flex items-center justify-center mx-auto">
                        <span class="text-white text-2xl">🍛</span>
                    </div>
                    <h3 class="mt-4 text-xl font-semibold text-gray-900">Culinary Delights</h3>
                    <p class="mt-2 text-gray-600">Taste authentic Pakistani cuisine and local specialties</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold text-gray-900">Welcome Back</h2>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <form onsubmit="handleLogin(event)">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                    </div>
                    <button type="submit" class="w-full bg-pakistan-green text-white py-2 px-4 rounded-md hover:bg-pakistan-light transition duration-300">
                        Sign In
                    </button>
                </form>
                <p class="mt-4 text-center text-sm text-gray-600">
                    Don't have an account? 
                    <button onclick="showSignup()" class="text-pakistan-green hover:text-pakistan-light font-semibold">Sign up</button>
                </p>
            </div>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold text-gray-900">Join Visit Pakistan</h2>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <form onsubmit="handleSignup(event)">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Full Name</label>
                        <input type="text" name="fullname" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                    </div>
                    <button type="submit" class="w-full bg-pakistan-green text-white py-2 px-4 rounded-md hover:bg-pakistan-light transition duration-300">
                        Create Account
                    </button>
                </form>
                <p class="mt-4 text-center text-sm text-gray-600">
                    Already have an account? 
                    <button onclick="showLogin()" class="text-pakistan-green hover:text-pakistan-light font-semibold">Sign in</button>
                </p>
            </div>
        </div>
    </div>

   <script src="includes/media/index.js"></script>
   <script src="includes/media/central.js"></script>

<?php
include "includes/footer.php";
?>